export interface AdminPanelTenant {
  id: string;
  domain: string;
  status: string;
  apiUrl: string;
  siteUrl: string;
  contactEmail: string;
  main: boolean;
  subscriptionStartDate: string;
  subscriptionEndDate: string;
  autoRenewal: boolean;
  mainCurrency: string;
  availableCurrencies: string;
  createdAt: string;
  updatedAt: string;
  
  // User information for the tenant owner
  ownerUserName: string;
  ownerEmail: string;
  totalUsers: number;
}
