package tndung.vnfb.smm.dto.response;

import lombok.Data;
import tndung.vnfb.smm.constant.enums.CommonStatus;

import java.math.BigDecimal;
import java.time.OffsetDateTime;

@Data
public class AdminPanelUserRes {
    private Long id;
    private String userName;
    private String email;
    private String phone;
    private BigDecimal balance;
    private CommonStatus status;
    private String timeZone;
    private String language;
    private OffsetDateTime lastLoginAt;
    private OffsetDateTime createdAt;
    private OffsetDateTime updatedAt;
    
    // Additional info for main tenant users
    private String tenantId;
    private String tenantDomain;
    private Integer totalOrders;
    private BigDecimal totalSpent;
}
