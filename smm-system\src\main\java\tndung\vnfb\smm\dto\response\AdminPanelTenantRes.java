package tndung.vnfb.smm.dto.response;

import lombok.Data;
import tndung.vnfb.smm.constant.enums.TenantStatus;

import java.time.ZonedDateTime;
import java.time.OffsetDateTime;

@Data
public class AdminPanelTenantRes {
    private String id;
    private String domain;
    private TenantStatus status;
    private String apiUrl;
    private String siteUrl;
    private String contactEmail;
    private Boolean main;
    private ZonedDateTime subscriptionStartDate;
    private ZonedDateTime subscriptionEndDate;
    private Boolean autoRenewal;
    private String mainCurrency;
    private String availableCurrencies;
    private OffsetDateTime createdAt;
    private OffsetDateTime updatedAt;
    
    // User information for the tenant owner
    private String ownerUserName;
    private String ownerEmail;
    private Integer totalUsers;
}
