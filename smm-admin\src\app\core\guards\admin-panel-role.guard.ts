import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { AuthUtilsService } from '../services/auth-utils.service';
import { isPlatformBrowser } from '@angular/common';
import { PLATFORM_ID } from '@angular/core';
import { ToastService } from '../services/toast.service';
import { NotifyType } from '../../constant/notify-type';
import { Role } from '../../constant/role';
import { JwtHelperService } from '@auth0/angular-jwt';

export const adminPanelRoleGuard: CanActivateFn = () => {
  const authUtils = inject(AuthUtilsService);
  const router = inject(Router);
  const platformId = inject(PLATFORM_ID);
  const jwtHelper = inject(JwtHelperService);
  const toastService = inject(ToastService);
  const isBrowser = isPlatformBrowser(platformId);

  // If we're not in a browser environment, deny access (for SSR)
  if (!isBrowser) {
    return false;
  }

  // First check if user is authenticated
  if (!authUtils.isAuthenticated() || authUtils.isTokenExpired()) {
    console.log('adminPanelRoleGuard - User is not authenticated or token is expired');
    router.navigate(['/auth/login']);
    return false;
  }

  // Check if user has ADMIN_PANEL role
  const user = authUtils.getUserFromStorage();
  if (!user || !user.tokens || !user.tokens.access_token) {
    console.log('adminPanelRoleGuard - No valid user or token, denying access');
    router.navigate(['/auth/login']);
    return false;
  }

  try {
    const tokenPayload = jwtHelper.decodeToken(user.tokens.access_token);
    const roles = tokenPayload.roles;

    if (!roles) {
      console.log('adminPanelRoleGuard - No roles in token payload, denying access');
      toastService.showToast('Bạn không có quyền truy cập trang quản trị panel', NotifyType.ERROR);
      router.navigate(['/error/403']);
      return false;
    }

    // Check if roles is an array and contains ADMIN_PANEL
    if (Array.isArray(roles) && roles.includes(Role.ADMIN_PANEL)) {
      console.log('adminPanelRoleGuard - User has ADMIN_PANEL role, allowing access');
      return true;
    }

    console.log('adminPanelRoleGuard - User does not have ADMIN_PANEL role, denying access');
    toastService.showToast('Bạn không có quyền truy cập trang quản trị panel', NotifyType.ERROR);
    router.navigate(['/error/403']);
    return false;
  } catch (error) {
    console.error('adminPanelRoleGuard - Error checking ADMIN_PANEL role:', error);
    toastService.showToast('Lỗi xác thực quyền truy cập', NotifyType.ERROR);
    router.navigate(['/auth/login']);
    return false;
  }
};
