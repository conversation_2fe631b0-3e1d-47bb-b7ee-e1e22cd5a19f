.admin-panel-container {
  min-height: 100vh;
  background-color: #f9fafb;
}

.panels-tab,
.users-tab {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Table styling */
table {
  border-collapse: separate;
  border-spacing: 0;
}

th {
  position: sticky;
  top: 0;
  z-index: 10;
}

/* Status badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Modal styling */
.modal-overlay {
  backdrop-filter: blur(4px);
}

/* Loading spinner */
.loading-spinner {
  border: 2px solid #f3f4f6;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive table */
@media (max-width: 768px) {
  .table-container {
    overflow-x: auto;
  }
  
  table {
    min-width: 600px;
  }
}

/* Button hover effects */
.btn-primary {
  transition: all 0.2s ease-in-out;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

/* Search input styling */
.search-input {
  transition: all 0.2s ease-in-out;
}

.search-input:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Tab navigation */
.tab-nav {
  border-bottom: 1px solid #e5e7eb;
}

.tab-button {
  position: relative;
  transition: all 0.2s ease-in-out;
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #3b82f6;
}

/* Pagination styling */
.pagination-button {
  transition: all 0.2s ease-in-out;
}

.pagination-button:hover:not(:disabled) {
  background-color: #f3f4f6;
  transform: translateY(-1px);
}

.pagination-button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* Card styling */
.card {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: box-shadow 0.2s ease-in-out;
}

.card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Form styling */
.form-input {
  transition: all 0.2s ease-in-out;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Action buttons */
.action-button {
  transition: all 0.2s ease-in-out;
}

.action-button:hover {
  transform: translateY(-1px);
}

/* Empty state */
.empty-state {
  padding: 3rem 1rem;
  text-align: center;
  color: #6b7280;
}

/* Mobile responsiveness */
@media (max-width: 640px) {
  .admin-panel-container {
    padding: 1rem;
  }
  
  .search-controls {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .search-controls .flex {
    width: 100%;
  }
  
  .table-container {
    border-radius: 0.5rem;
    overflow: hidden;
  }
}
