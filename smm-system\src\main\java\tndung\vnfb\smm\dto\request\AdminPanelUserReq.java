package tndung.vnfb.smm.dto.request;

import lombok.Data;
import tndung.vnfb.smm.constant.enums.TransactionSource;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;

@Data
public class AdminPanelUserReq {
    
    @NotNull(message = "User ID is required")
    private Long userId;
    
    @NotNull(message = "Amount is required")
    @Positive(message = "Amount must be positive")
    private BigDecimal amount;
    
    private TransactionSource source = TransactionSource.BONUS;
    
    private String note;
}
