import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { tap } from 'rxjs/operators';
import { ConfigService } from './config.service';
import { AdminPanelTenant } from '../../model/response/admin-panel-tenant.model';
import { AdminPanelUser } from '../../model/response/admin-panel-user.model';
import { AdminPanelUserRequest } from '../../model/request/admin-panel-user.model';
import { TransactionRes } from '../../model/response/transaction.model';

export interface PageResponse<T> {
  content: T[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
}

@Injectable({
  providedIn: 'root'
})
export class AdminPanelService {
  private readonly baseUrl: string;

  // State management
  private panelsSubject = new BehaviorSubject<AdminPanelTenant[]>([]);
  private usersSubject = new BehaviorSubject<AdminPanelUser[]>([]);
  private loadingSubject = new BehaviorSubject<boolean>(false);
  private panelsPaginationSubject = new BehaviorSubject<any>({
    pageNumber: 0,
    pageSize: 10,
    totalElements: 0,
    totalPages: 0
  });
  private usersPaginationSubject = new BehaviorSubject<any>({
    pageNumber: 0,
    pageSize: 10,
    totalElements: 0,
    totalPages: 0
  });

  // Observables
  public panels$ = this.panelsSubject.asObservable();
  public users$ = this.usersSubject.asObservable();
  public loading$ = this.loadingSubject.asObservable();
  public panelsPagination$ = this.panelsPaginationSubject.asObservable();
  public usersPagination$ = this.usersPaginationSubject.asObservable();

  constructor(
    private http: HttpClient,
    private configService: ConfigService
  ) {
    this.baseUrl = `${this.configService.apiUrl}/admin-panels`;
  }

  /**
   * Get all panels (tenants with main = false)
   */
  getAllPanels(page: number = 0, size: number = 10, search?: string): Observable<PageResponse<AdminPanelTenant>> {
    this.loadingSubject.next(true);

    let params = new HttpParams()
      .set('page', page.toString())
      .set('size', size.toString());

    if (search && search.trim()) {
      params = params.set('search', search.trim());
    }

    return this.http.get<PageResponse<AdminPanelTenant>>(`${this.baseUrl}/panels`, { params })
      .pipe(
        tap(response => {
          this.panelsSubject.next(response.content);
          this.panelsPaginationSubject.next({
            pageNumber: response.number,
            pageSize: response.size,
            totalElements: response.totalElements,
            totalPages: response.totalPages
          });
          this.loadingSubject.next(false);
        })
      );
  }

  /**
   * Get all users from main tenant
   */
  getMainTenantUsers(page: number = 0, size: number = 10, search?: string): Observable<PageResponse<AdminPanelUser>> {
    this.loadingSubject.next(true);

    let params = new HttpParams()
      .set('page', page.toString())
      .set('size', size.toString());

    if (search && search.trim()) {
      params = params.set('search', search.trim());
    }

    return this.http.get<PageResponse<AdminPanelUser>>(`${this.baseUrl}/main-tenant-users`, { params })
      .pipe(
        tap(response => {
          this.usersSubject.next(response.content);
          this.usersPaginationSubject.next({
            pageNumber: response.number,
            pageSize: response.size,
            totalElements: response.totalElements,
            totalPages: response.totalPages
          });
          this.loadingSubject.next(false);
        })
      );
  }

  /**
   * Add money to a main tenant user
   */
  addMoneyToUser(request: AdminPanelUserRequest): Observable<TransactionRes> {
    this.loadingSubject.next(true);

    return this.http.post<TransactionRes>(`${this.baseUrl}/add-money`, request)
      .pipe(
        tap(() => {
          this.loadingSubject.next(false);
        })
      );
  }

  /**
   * Get tenant details by ID
   */
  getTenantById(tenantId: string): Observable<AdminPanelTenant> {
    return this.http.get<AdminPanelTenant>(`${this.baseUrl}/panels/${tenantId}`);
  }

  /**
   * Get user details by ID
   */
  getUserById(userId: number): Observable<AdminPanelUser> {
    return this.http.get<AdminPanelUser>(`${this.baseUrl}/users/${userId}`);
  }

  // Getters for current values
  get panelsValue(): AdminPanelTenant[] {
    return this.panelsSubject.value;
  }

  get usersValue(): AdminPanelUser[] {
    return this.usersSubject.value;
  }

  get panelsPaginationValue(): any {
    return this.panelsPaginationSubject.value;
  }

  get usersPaginationValue(): any {
    return this.usersPaginationSubject.value;
  }
}
